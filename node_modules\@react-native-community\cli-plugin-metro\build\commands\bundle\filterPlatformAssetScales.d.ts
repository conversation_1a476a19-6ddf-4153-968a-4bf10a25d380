/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
declare function filterPlatformAssetScales(platform: string, scales: ReadonlyArray<number>): ReadonlyArray<number>;
export default filterPlatformAssetScales;
//# sourceMappingURL=filterPlatformAssetScales.d.ts.map