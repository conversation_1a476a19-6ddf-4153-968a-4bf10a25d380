{"name": "@urql/exchange-retry", "version": "1.2.0", "description": "An exchange for operation retry support in urql", "sideEffects": false, "homepage": "https://formidable.com/open-source/urql/docs/", "bugs": "https://github.com/urql-graphql/urql/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/urql-graphql/urql.git", "directory": "exchanges/retry"}, "keywords": ["urql", "graphql client", "graphql", "exchanges", "retry"], "main": "dist/urql-exchange-retry", "module": "dist/urql-exchange-retry.mjs", "types": "dist/urql-exchange-retry.d.ts", "source": "src/index.ts", "exports": {".": {"types": "./dist/urql-exchange-retry.d.ts", "import": "./dist/urql-exchange-retry.mjs", "require": "./dist/urql-exchange-retry.js", "source": "./src/index.ts"}, "./package.json": "./package.json"}, "files": ["LICENSE", "CHANGELOG.md", "README.md", "dist/"], "devDependencies": {"graphql": "^16.0.0", "@urql/core": "4.0.8"}, "dependencies": {"@urql/core": ">=4.0.0", "wonka": "^6.3.2"}, "publishConfig": {"access": "public", "provenance": true}, "scripts": {"test": "vitest --config ../../vitest.config.ts", "clean": "<PERSON><PERSON><PERSON> dist", "check": "tsc --noEmit", "lint": "eslint --ext=js,jsx,ts,tsx .", "build": "rollup -c ../../scripts/rollup/config.mjs"}}