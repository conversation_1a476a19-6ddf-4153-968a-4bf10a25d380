{"name": "hanzi-flashcard-app", "version": "1.0.0", "description": "Smart Chinese Character Learning App for iOS and Android", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:ios": "expo build:ios", "build:android": "expo build:android", "eject": "expo eject"}, "dependencies": {"expo": "~49.0.0", "expo-av": "~13.4.1", "expo-speech": "~11.3.0", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-svg": "13.9.0", "@react-native-async-storage/async-storage": "1.18.2", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "typescript": "^5.1.3"}, "keywords": ["chinese", "hanzi", "flashcard", "learning", "education", "react-native", "expo", "ios", "android"], "author": "Your Name", "license": "MIT", "private": true}