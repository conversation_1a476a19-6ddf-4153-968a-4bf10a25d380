{"name": "@types/yargs", "version": "16.0.9", "description": "TypeScript definitions for yargs", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>", "url": "https://github.com/poelstra"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana", "url": "https://github.com/mizunashi-mana"}, {"name": "<PERSON><PERSON>", "githubUsername": "pushplay", "url": "https://github.com/pushplay"}, {"name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC", "url": "https://github.com/JimiC"}, {"name": "Steffen Viken Valvåg", "githubUsername": "steffenvv", "url": "https://github.com/steffenvv"}, {"name": "<PERSON>", "githubUsername": "forivall", "url": "https://github.com/forivall"}, {"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Aankhen"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/yargs"}, "scripts": {}, "dependencies": {"@types/yargs-parser": "*"}, "typesPublisherContentHash": "cb1afff2e12b5d722156de0eabb2bd446fd1eb57638052e13771b041d96e0e52", "typeScriptVersion": "4.5"}