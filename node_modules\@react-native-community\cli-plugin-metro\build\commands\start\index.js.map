{"version": 3, "names": ["name", "func", "runServer", "description", "options", "parse", "Number", "default", "val", "path", "resolve", "split", "map", "folder", "workers"], "sources": ["../../../src/commands/start/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport runServer from './runServer';\n\nexport default {\n  name: 'start',\n  func: runServer,\n  description: 'starts the webserver',\n  options: [\n    {\n      name: '--port <number>',\n      parse: Number,\n    },\n    {\n      name: '--host <string>',\n      default: '',\n    },\n    {\n      name: '--projectRoot <path>',\n      description: 'Path to a custom project root',\n      parse: (val: string) => path.resolve(val),\n    },\n    {\n      name: '--watchFolders <list>',\n      description:\n        'Specify any additional folders to be added to the watch list',\n      parse: (val: string) =>\n        val.split(',').map<string>((folder: string) => path.resolve(folder)),\n    },\n    {\n      name: '--assetPlugins <list>',\n      description:\n        'Specify any additional asset plugins to be used by the packager by full filepath',\n      parse: (val: string) => val.split(','),\n    },\n    {\n      name: '--sourceExts <list>',\n      description:\n        'Specify any additional source extensions to be used by the packager',\n      parse: (val: string) => val.split(','),\n    },\n    {\n      name: '--max-workers <number>',\n      description:\n        'Specifies the maximum number of workers the worker-pool ' +\n        'will spawn for transforming files. This defaults to the number of the ' +\n        'cores available on your machine.',\n      parse: (workers: string) => Number(workers),\n    },\n    {\n      name: '--transformer <string>',\n      description: 'Specify a custom transformer to be used',\n    },\n    {\n      name: '--reset-cache, --resetCache',\n      description: 'Removes cached files',\n    },\n    {\n      name: '--custom-log-reporter-path, --customLogReporterPath <string>',\n      description:\n        'Path to a JavaScript file that exports a log reporter as a replacement for TerminalReporter',\n    },\n    {\n      name: '--https',\n      description: 'Enables https connections to the server',\n    },\n    {\n      name: '--key <path>',\n      description: 'Path to custom SSL key',\n    },\n    {\n      name: '--cert <path>',\n      description: 'Path to custom SSL cert',\n    },\n    {\n      name: '--config <string>',\n      description: 'Path to the CLI configuration file',\n      parse: (val: string) => path.resolve(val),\n    },\n    {\n      name: '--no-interactive',\n      description: 'Disables interactive mode',\n    },\n  ],\n};\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAoC;AATpC;AACA;AACA;AACA;AACA;AACA;AACA;AANA,eAWe;EACbA,IAAI,EAAE,OAAO;EACbC,IAAI,EAAEC,kBAAS;EACfC,WAAW,EAAE,sBAAsB;EACnCC,OAAO,EAAE,CACP;IACEJ,IAAI,EAAE,iBAAiB;IACvBK,KAAK,EAAEC;EACT,CAAC,EACD;IACEN,IAAI,EAAE,iBAAiB;IACvBO,OAAO,EAAE;EACX,CAAC,EACD;IACEP,IAAI,EAAE,sBAAsB;IAC5BG,WAAW,EAAE,+BAA+B;IAC5CE,KAAK,EAAGG,GAAW,IAAKC,eAAI,CAACC,OAAO,CAACF,GAAG;EAC1C,CAAC,EACD;IACER,IAAI,EAAE,uBAAuB;IAC7BG,WAAW,EACT,8DAA8D;IAChEE,KAAK,EAAGG,GAAW,IACjBA,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAUC,MAAc,IAAKJ,eAAI,CAACC,OAAO,CAACG,MAAM,CAAC;EACvE,CAAC,EACD;IACEb,IAAI,EAAE,uBAAuB;IAC7BG,WAAW,EACT,kFAAkF;IACpFE,KAAK,EAAGG,GAAW,IAAKA,GAAG,CAACG,KAAK,CAAC,GAAG;EACvC,CAAC,EACD;IACEX,IAAI,EAAE,qBAAqB;IAC3BG,WAAW,EACT,qEAAqE;IACvEE,KAAK,EAAGG,GAAW,IAAKA,GAAG,CAACG,KAAK,CAAC,GAAG;EACvC,CAAC,EACD;IACEX,IAAI,EAAE,wBAAwB;IAC9BG,WAAW,EACT,0DAA0D,GAC1D,wEAAwE,GACxE,kCAAkC;IACpCE,KAAK,EAAGS,OAAe,IAAKR,MAAM,CAACQ,OAAO;EAC5C,CAAC,EACD;IACEd,IAAI,EAAE,wBAAwB;IAC9BG,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,6BAA6B;IACnCG,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,8DAA8D;IACpEG,WAAW,EACT;EACJ,CAAC,EACD;IACEH,IAAI,EAAE,SAAS;IACfG,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,cAAc;IACpBG,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,eAAe;IACrBG,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,mBAAmB;IACzBG,WAAW,EAAE,oCAAoC;IACjDE,KAAK,EAAGG,GAAW,IAAKC,eAAI,CAACC,OAAO,CAACF,GAAG;EAC1C,CAAC,EACD;IACER,IAAI,EAAE,kBAAkB;IACxBG,WAAW,EAAE;EACf,CAAC;AAEL,CAAC;AAAA"}