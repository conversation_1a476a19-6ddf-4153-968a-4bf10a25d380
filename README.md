# 汉字大师 - <PERSON>zi Master Flashcard App

A comprehensive Chinese character learning app built with React Native and Expo, designed to run on iPhone and iPad.

## 🎯 Features

- **800 High-Frequency Chinese Characters** from HSK levels 1-5
- **Three Learning Modes**: Recognition, Writing, and Listening
- **Smart Spaced Repetition System (SRS)** for optimal learning
- **Audio Pronunciation** with native Chinese speech synthesis
- **Progress Tracking** with detailed statistics
- **Dark Mode Support** for comfortable learning
- **Favorites System** to bookmark difficult characters
- **Search and Filter** functionality
- **Offline Learning** - works without internet connection
- **iPad Optimized** with responsive design

## 📱 Platform Support

- ✅ iPhone (iOS 11.0+)
- ✅ iPad (iPadOS 13.0+)
- ✅ Android (API 21+)
- ✅ Web (Progressive Web App)

## 🚀 Quick Start

### Prerequisites

1. Install Node.js (v16 or later)
2. Install Expo CLI: `npm install -g @expo/cli`
3. Install Expo Go app on your iPhone/iPad from the App Store

### Installation

1. Clone or download this project
2. Navigate to the project directory
3. Install dependencies:
   ```bash
   npm install
   ```

### Running on iPhone/iPad

#### Method 1: Using Expo Go (Recommended for Development)

1. Start the development server:
   ```bash
   npm start
   ```

2. Open Expo Go app on your iPhone/iPad

3. Scan the QR code displayed in the terminal or browser

4. The app will load on your device

#### Method 2: iOS Simulator (macOS only)

1. Install Xcode from the Mac App Store
2. Run:
   ```bash
   npm run ios
   ```

#### Method 3: Building for Production

1. Create an Expo account at https://expo.dev
2. Login to Expo CLI:
   ```bash
   expo login
   ```

3. Build for iOS:
   ```bash
   expo build:ios
   ```

4. Follow the prompts to configure your Apple Developer account

## 🛠 Development

### Project Structure

```
├── App.tsx                 # Main app entry point
├── hanzi-flashcard-app.tsx # Main app component
├── package.json           # Dependencies and scripts
├── app.json              # Expo configuration
└── assets/               # Images and icons
```

### Key Dependencies

- **Expo**: Cross-platform development framework
- **React Native**: Mobile app framework
- **expo-speech**: Text-to-speech functionality
- **expo-av**: Audio playback
- **react-native-svg**: Vector graphics for icons
- **@react-native-async-storage/async-storage**: Local data storage

## 📚 Learning Features

### HSK Levels
- **HSK 1**: 150 basic characters
- **HSK 2**: 300 elementary characters  
- **HSK 3**: 600 intermediate characters
- **HSK 4**: 1200 upper-intermediate characters
- **HSK 5**: 2500 advanced characters

### Learning Modes

1. **Recognition Mode**: See the character, recall meaning and pronunciation
2. **Writing Mode**: Practice writing characters with stroke guidance
3. **Listening Mode**: Hear pronunciation, identify the correct character

### Smart Features

- **Adaptive Learning**: Difficult characters appear more frequently
- **Progress Tracking**: Monitor your learning journey
- **Streak Counter**: Maintain daily learning habits
- **Statistics**: Detailed analytics on your performance

## 🎨 Customization

The app supports extensive customization:

- **Dark/Light Mode**: Automatic or manual theme switching
- **Daily Goals**: Set custom learning targets
- **Audio Settings**: Enable/disable pronunciation
- **Hint System**: Toggle learning aids
- **Auto-play**: Automatic audio pronunciation

## 📊 Data Management

- **Local Storage**: All progress saved locally on device
- **Export/Import**: Backup and restore your learning data
- **Privacy**: No data sent to external servers

## 🔧 Troubleshooting

### Common Issues

1. **App won't load on device**:
   - Ensure your device and computer are on the same WiFi network
   - Try restarting the Expo development server

2. **Audio not working**:
   - Check device volume settings
   - Ensure audio permissions are granted

3. **Performance issues**:
   - Close other apps to free up memory
   - Restart the app if it becomes sluggish

### iOS Specific

- **Requires iOS 11.0 or later**
- **Works on all iPhone models from iPhone 6s onwards**
- **Optimized for iPad with larger screen layouts**

## 📄 License

MIT License - feel free to use this project for educational purposes.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues and enhancement requests.

## 📞 Support

If you encounter any issues or have questions, please create an issue in the project repository.

---

**Happy Learning! 加油！** 🇨🇳📚
