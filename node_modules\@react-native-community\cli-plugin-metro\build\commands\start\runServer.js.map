{"version": 3, "names": ["runServer", "_argv", "ctx", "args", "reportEvent", "terminal", "Terminal", "process", "stdout", "ReporterImpl", "getReporterImpl", "customLogReporterPath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reporter", "update", "event", "metroConfig", "loadMetroConfig", "config", "maxWorkers", "port", "resetCache", "watchFolders", "projectRoot", "sourceExts", "assetPlugins", "transformer", "map", "plugin", "require", "resolve", "middleware", "websocketEndpoints", "messageSocketEndpoint", "eventsSocketEndpoint", "createDevServerMiddleware", "host", "server", "use", "indexPageMiddleware", "customEnhanceMiddleware", "enhanceMiddleware", "metroMiddleware", "serverInstance", "Metro", "secure", "https", "secureCert", "cert", "<PERSON><PERSON><PERSON>", "key", "interactive", "enableWatchMode", "keepAliveTimeout", "version", "logIfUpdateAvailable", "root", "undefined", "e", "code", "path"], "sources": ["../../../src/commands/start/runServer.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n// @ts-ignore untyped metro\nimport Metro from 'metro';\nimport type Server from 'metro/src/Server';\nimport type {Middleware} from 'metro-config';\nimport {Terminal} from 'metro-core';\nimport path from 'path';\nimport {\n  createDevServerMiddleware,\n  indexPageMiddleware,\n} from '@react-native-community/cli-server-api';\nimport {Config} from '@react-native-community/cli-types';\n\nimport loadMetroConfig from '../../tools/loadMetroConfig';\nimport {version} from '@react-native-community/cli-tools';\nimport enableWatchMode from './watchMode';\n\nexport type Args = {\n  assetPlugins?: string[];\n  cert?: string;\n  customLogReporterPath?: string;\n  host?: string;\n  https?: boolean;\n  maxWorkers?: number;\n  key?: string;\n  platforms?: string[];\n  port?: number;\n  resetCache?: boolean;\n  sourceExts?: string[];\n  transformer?: string;\n  watchFolders?: string[];\n  config?: string;\n  projectRoot?: string;\n  interactive: boolean;\n};\n\nasync function runServer(_argv: Array<string>, ctx: Config, args: Args) {\n  let reportEvent: ((event: any) => void) | undefined;\n  const terminal = new Terminal(process.stdout);\n  const ReporterImpl = getReporterImpl(args.customLogReporterPath);\n  const terminalReporter = new ReporterImpl(terminal);\n  const reporter = {\n    update(event: any) {\n      terminalReporter.update(event);\n      if (reportEvent) {\n        reportEvent(event);\n      }\n    },\n  };\n\n  const metroConfig = await loadMetroConfig(ctx, {\n    config: args.config,\n    maxWorkers: args.maxWorkers,\n    port: args.port,\n    resetCache: args.resetCache,\n    watchFolders: args.watchFolders,\n    projectRoot: args.projectRoot,\n    sourceExts: args.sourceExts,\n    reporter,\n  });\n\n  if (args.assetPlugins) {\n    // @ts-ignore - assigning to readonly property\n    metroConfig.transformer.assetPlugins = args.assetPlugins.map((plugin) =>\n      require.resolve(plugin),\n    );\n  }\n\n  const {\n    middleware,\n    websocketEndpoints,\n    messageSocketEndpoint,\n    eventsSocketEndpoint,\n  } = createDevServerMiddleware({\n    host: args.host,\n    port: metroConfig.server.port,\n    watchFolders: metroConfig.watchFolders,\n  });\n  middleware.use(indexPageMiddleware);\n\n  const customEnhanceMiddleware = metroConfig.server.enhanceMiddleware;\n  // @ts-ignore - assigning to readonly property\n  metroConfig.server.enhanceMiddleware = (\n    metroMiddleware: Middleware,\n    server: Server,\n  ) => {\n    if (customEnhanceMiddleware) {\n      metroMiddleware = customEnhanceMiddleware(metroMiddleware, server);\n    }\n    return middleware.use(metroMiddleware);\n  };\n\n  const serverInstance = await Metro.runServer(metroConfig, {\n    host: args.host,\n    secure: args.https,\n    secureCert: args.cert,\n    secureKey: args.key,\n    // @ts-ignore - ws.Server types are incompatible\n    websocketEndpoints,\n  });\n\n  reportEvent = eventsSocketEndpoint.reportEvent;\n\n  if (args.interactive) {\n    enableWatchMode(messageSocketEndpoint);\n  }\n\n  // In Node 8, the default keep-alive for an HTTP connection is 5 seconds. In\n  // early versions of Node 8, this was implemented in a buggy way which caused\n  // some HTTP responses (like those containing large JS bundles) to be\n  // terminated early.\n  //\n  // As a workaround, arbitrarily increase the keep-alive from 5 to 30 seconds,\n  // which should be enough to send even the largest of JS bundles.\n  //\n  // For more info: https://github.com/nodejs/node/issues/13391\n  //\n  serverInstance.keepAliveTimeout = 30000;\n\n  await version.logIfUpdateAvailable(ctx.root);\n}\n\nfunction getReporterImpl(customLogReporterPath: string | undefined) {\n  if (customLogReporterPath === undefined) {\n    return require('metro/src/lib/TerminalReporter');\n  }\n  try {\n    // First we let require resolve it, so we can require packages in node_modules\n    // as expected. eg: require('my-package/reporter');\n    return require(customLogReporterPath);\n  } catch (e) {\n    if ((<any>e).code !== 'MODULE_NOT_FOUND') {\n      throw e;\n    }\n    // If that doesn't work, then we next try relative to the cwd, eg:\n    // require('./reporter');\n    return require(path.resolve(customLogReporterPath));\n  }\n}\n\nexport default runServer;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAGA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA0C;AArB1C;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAmCA,eAAeA,SAAS,CAACC,KAAoB,EAAEC,GAAW,EAAEC,IAAU,EAAE;EACtE,IAAIC,WAA+C;EACnD,MAAMC,QAAQ,GAAG,KAAIC,qBAAQ,EAACC,OAAO,CAACC,MAAM,CAAC;EAC7C,MAAMC,YAAY,GAAGC,eAAe,CAACP,IAAI,CAACQ,qBAAqB,CAAC;EAChE,MAAMC,gBAAgB,GAAG,IAAIH,YAAY,CAACJ,QAAQ,CAAC;EACnD,MAAMQ,QAAQ,GAAG;IACfC,MAAM,CAACC,KAAU,EAAE;MACjBH,gBAAgB,CAACE,MAAM,CAACC,KAAK,CAAC;MAC9B,IAAIX,WAAW,EAAE;QACfA,WAAW,CAACW,KAAK,CAAC;MACpB;IACF;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAM,IAAAC,wBAAe,EAACf,GAAG,EAAE;IAC7CgB,MAAM,EAAEf,IAAI,CAACe,MAAM;IACnBC,UAAU,EAAEhB,IAAI,CAACgB,UAAU;IAC3BC,IAAI,EAAEjB,IAAI,CAACiB,IAAI;IACfC,UAAU,EAAElB,IAAI,CAACkB,UAAU;IAC3BC,YAAY,EAAEnB,IAAI,CAACmB,YAAY;IAC/BC,WAAW,EAAEpB,IAAI,CAACoB,WAAW;IAC7BC,UAAU,EAAErB,IAAI,CAACqB,UAAU;IAC3BX;EACF,CAAC,CAAC;EAEF,IAAIV,IAAI,CAACsB,YAAY,EAAE;IACrB;IACAT,WAAW,CAACU,WAAW,CAACD,YAAY,GAAGtB,IAAI,CAACsB,YAAY,CAACE,GAAG,CAAEC,MAAM,IAClEC,OAAO,CAACC,OAAO,CAACF,MAAM,CAAC,CACxB;EACH;EAEA,MAAM;IACJG,UAAU;IACVC,kBAAkB;IAClBC,qBAAqB;IACrBC;EACF,CAAC,GAAG,IAAAC,yCAAyB,EAAC;IAC5BC,IAAI,EAAEjC,IAAI,CAACiC,IAAI;IACfhB,IAAI,EAAEJ,WAAW,CAACqB,MAAM,CAACjB,IAAI;IAC7BE,YAAY,EAAEN,WAAW,CAACM;EAC5B,CAAC,CAAC;EACFS,UAAU,CAACO,GAAG,CAACC,mCAAmB,CAAC;EAEnC,MAAMC,uBAAuB,GAAGxB,WAAW,CAACqB,MAAM,CAACI,iBAAiB;EACpE;EACAzB,WAAW,CAACqB,MAAM,CAACI,iBAAiB,GAAG,CACrCC,eAA2B,EAC3BL,MAAc,KACX;IACH,IAAIG,uBAAuB,EAAE;MAC3BE,eAAe,GAAGF,uBAAuB,CAACE,eAAe,EAAEL,MAAM,CAAC;IACpE;IACA,OAAON,UAAU,CAACO,GAAG,CAACI,eAAe,CAAC;EACxC,CAAC;EAED,MAAMC,cAAc,GAAG,MAAMC,gBAAK,CAAC5C,SAAS,CAACgB,WAAW,EAAE;IACxDoB,IAAI,EAAEjC,IAAI,CAACiC,IAAI;IACfS,MAAM,EAAE1C,IAAI,CAAC2C,KAAK;IAClBC,UAAU,EAAE5C,IAAI,CAAC6C,IAAI;IACrBC,SAAS,EAAE9C,IAAI,CAAC+C,GAAG;IACnB;IACAlB;EACF,CAAC,CAAC;EAEF5B,WAAW,GAAG8B,oBAAoB,CAAC9B,WAAW;EAE9C,IAAID,IAAI,CAACgD,WAAW,EAAE;IACpB,IAAAC,kBAAe,EAACnB,qBAAqB,CAAC;EACxC;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAU,cAAc,CAACU,gBAAgB,GAAG,KAAK;EAEvC,MAAMC,mBAAO,CAACC,oBAAoB,CAACrD,GAAG,CAACsD,IAAI,CAAC;AAC9C;AAEA,SAAS9C,eAAe,CAACC,qBAAyC,EAAE;EAClE,IAAIA,qBAAqB,KAAK8C,SAAS,EAAE;IACvC,OAAO5B,OAAO,CAAC,gCAAgC,CAAC;EAClD;EACA,IAAI;IACF;IACA;IACA,OAAOA,OAAO,CAAClB,qBAAqB,CAAC;EACvC,CAAC,CAAC,OAAO+C,CAAC,EAAE;IACV,IAAUA,CAAC,CAAEC,IAAI,KAAK,kBAAkB,EAAE;MACxC,MAAMD,CAAC;IACT;IACA;IACA;IACA,OAAO7B,OAAO,CAAC+B,eAAI,CAAC9B,OAAO,CAACnB,qBAAqB,CAAC,CAAC;EACrD;AACF;AAAC,eAEcX,SAAS;AAAA"}