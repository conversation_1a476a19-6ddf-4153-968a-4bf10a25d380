{"version": 3, "names": ["runPack<PERSON>", "args", "config", "packager", "result", "isPackagerRunning", "port", "logger", "info", "warn", "startServerInNewWindow", "terminal", "root", "reactNativePath", "error", "Error", "message", "buildAndroid", "_argv", "androidProject", "getAndroidProject", "variant", "tasks", "mode", "interactive", "selectedTask", "promptForTaskSelection", "sourceDir", "grad<PERSON><PERSON><PERSON><PERSON>", "getTaskNames", "appName", "extraParams", "push", "activeArchOnly", "adbPath", "getAdbPath", "devices", "adb", "getDevices", "architectures", "map", "device", "getCPU", "filter", "arch", "index", "array", "indexOf", "length", "join", "build", "process", "chdir", "cmd", "platform", "startsWith", "debug", "execa", "sync", "stdio", "cwd", "printRunDoctorTip", "CLIError", "options", "name", "description", "default", "env", "RCT_METRO_PORT", "parse", "Number", "getDefaultUserTerminal", "val", "split", "func"], "sources": ["../../../src/commands/buildAndroid/index.ts"], "sourcesContent": ["import {\n  CLIError,\n  getDefaultUserTerminal,\n  isPackagerRunning,\n  logger,\n  printRunDoctorTip,\n} from '@react-native-community/cli-tools';\nimport {Config} from '@react-native-community/cli-types';\nimport execa from 'execa';\nimport {getAndroidProject} from '../../config/getAndroidProject';\nimport adb from '../runAndroid/adb';\nimport getAdbPath from '../runAndroid/getAdbPath';\nimport {startServerInNewWindow} from './startServerInNewWindow';\nimport {getTaskNames} from '../runAndroid/getTaskNames';\nimport {promptForTaskSelection} from '../runAndroid/listAndroidTasks';\n\nexport interface BuildFlags {\n  mode?: string;\n  variant?: string;\n  activeArchOnly?: boolean;\n  packager?: boolean;\n  port: number;\n  terminal: string;\n  tasks?: Array<string>;\n  extraParams?: Array<string>;\n  interactive?: boolean;\n}\n\nexport async function runPackager(args: BuildFlags, config: Config) {\n  if (!args.packager) {\n    return;\n  }\n  const result = await isPackagerRunning(args.port);\n  if (result === 'running') {\n    logger.info('JS server already running.');\n  } else if (result === 'unrecognized') {\n    logger.warn('JS server not recognized, continuing with build...');\n  } else {\n    // result == 'not_running'\n    logger.info('Starting JS server...');\n\n    try {\n      startServerInNewWindow(\n        args.port,\n        args.terminal,\n        config.root,\n        config.reactNativePath,\n      );\n    } catch (error) {\n      if (error instanceof Error) {\n        logger.warn(\n          `Failed to automatically start the packager server. Please run \"react-native start\" manually. Error details: ${error.message}`,\n        );\n      }\n    }\n  }\n}\n\nasync function buildAndroid(\n  _argv: Array<string>,\n  config: Config,\n  args: BuildFlags,\n) {\n  const androidProject = getAndroidProject(config);\n\n  if (args.variant) {\n    logger.warn(\n      '\"variant\" flag is deprecated and will be removed in future release. Please switch to \"mode\" flag.',\n    );\n  }\n\n  if (args.tasks && args.mode) {\n    logger.warn(\n      'Both \"tasks\" and \"mode\" parameters were passed to \"build\" command. Using \"tasks\" for building the app.',\n    );\n  }\n\n  let {tasks} = args;\n\n  if (args.interactive) {\n    const selectedTask = await promptForTaskSelection(\n      'build',\n      androidProject.sourceDir,\n    );\n    if (selectedTask) {\n      tasks = [selectedTask];\n    }\n  }\n\n  let gradleArgs = getTaskNames(\n    androidProject.appName,\n    args.mode || args.variant,\n    tasks,\n    'bundle',\n  );\n\n  if (args.extraParams) {\n    gradleArgs.push(...args.extraParams);\n  }\n\n  if (args.activeArchOnly) {\n    const adbPath = getAdbPath();\n    const devices = adb.getDevices(adbPath);\n    const architectures = devices\n      .map((device) => {\n        return adb.getCPU(adbPath, device);\n      })\n      .filter(\n        (arch, index, array) => arch != null && array.indexOf(arch) === index,\n      );\n    if (architectures.length > 0) {\n      logger.info(`Detected architectures ${architectures.join(', ')}`);\n      // `reactNativeDebugArchitectures` was renamed to `reactNativeArchitectures` in 0.68.\n      // Can be removed when 0.67 no longer needs to be supported.\n      gradleArgs.push(\n        '-PreactNativeDebugArchitectures=' + architectures.join(','),\n      );\n      gradleArgs.push('-PreactNativeArchitectures=' + architectures.join(','));\n    }\n  }\n  await runPackager(args, config);\n  return build(gradleArgs, androidProject.sourceDir);\n}\n\nexport function build(gradleArgs: string[], sourceDir: string) {\n  process.chdir(sourceDir);\n  const cmd = process.platform.startsWith('win') ? 'gradlew.bat' : './gradlew';\n  logger.info('Building the app...');\n  logger.debug(`Running command \"${cmd} ${gradleArgs.join(' ')}\"`);\n  try {\n    execa.sync(cmd, gradleArgs, {\n      stdio: 'inherit',\n      cwd: sourceDir,\n    });\n  } catch (error) {\n    printRunDoctorTip();\n    throw new CLIError('Failed to build the app.', error as Error);\n  }\n}\n\nexport const options = [\n  {\n    name: '--mode <string>',\n    description: \"Specify your app's build variant\",\n  },\n  {\n    name: '--variant <string>',\n    description:\n      \"Specify your app's build variant. Deprecated! Use 'mode' instead\",\n  },\n  {\n    name: '--no-packager',\n    description: 'Do not launch packager while building',\n  },\n  {\n    name: '--port <number>',\n    default: process.env.RCT_METRO_PORT || 8081,\n    parse: Number,\n  },\n  {\n    name: '--terminal <string>',\n    description:\n      'Launches the Metro Bundler in a new window using the specified terminal path.',\n    default: getDefaultUserTerminal(),\n  },\n  {\n    name: '--tasks <list>',\n    description:\n      'Run custom Gradle tasks. By default it\\'s \"assembleDebug\". Will override passed mode and variant arguments.',\n    parse: (val: string) => val.split(','),\n  },\n  {\n    name: '--active-arch-only',\n    description:\n      'Build native libraries only for the current device architecture for debug builds.',\n    default: false,\n  },\n  {\n    name: '--extra-params <string>',\n    description: 'Custom params passed to gradle build command',\n    parse: (val: string) => val.split(' '),\n  },\n  {\n    name: '--interactive',\n    description:\n      'Explicitly select build type and flavour to use before running a build',\n  },\n];\n\nexport default {\n  name: 'build-android',\n  description: 'builds your app',\n  func: buildAndroid,\n  options,\n};\n"], "mappings": ";;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAsE;AAc/D,eAAeA,WAAW,CAACC,IAAgB,EAAEC,MAAc,EAAE;EAClE,IAAI,CAACD,IAAI,CAACE,QAAQ,EAAE;IAClB;EACF;EACA,MAAMC,MAAM,GAAG,MAAM,IAAAC,6BAAiB,EAACJ,IAAI,CAACK,IAAI,CAAC;EACjD,IAAIF,MAAM,KAAK,SAAS,EAAE;IACxBG,kBAAM,CAACC,IAAI,CAAC,4BAA4B,CAAC;EAC3C,CAAC,MAAM,IAAIJ,MAAM,KAAK,cAAc,EAAE;IACpCG,kBAAM,CAACE,IAAI,CAAC,oDAAoD,CAAC;EACnE,CAAC,MAAM;IACL;IACAF,kBAAM,CAACC,IAAI,CAAC,uBAAuB,CAAC;IAEpC,IAAI;MACF,IAAAE,8CAAsB,EACpBT,IAAI,CAACK,IAAI,EACTL,IAAI,CAACU,QAAQ,EACbT,MAAM,CAACU,IAAI,EACXV,MAAM,CAACW,eAAe,CACvB;IACH,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,IAAIA,KAAK,YAAYC,KAAK,EAAE;QAC1BR,kBAAM,CAACE,IAAI,CACR,+GAA8GK,KAAK,CAACE,OAAQ,EAAC,CAC/H;MACH;IACF;EACF;AACF;AAEA,eAAeC,YAAY,CACzBC,KAAoB,EACpBhB,MAAc,EACdD,IAAgB,EAChB;EACA,MAAMkB,cAAc,GAAG,IAAAC,oCAAiB,EAAClB,MAAM,CAAC;EAEhD,IAAID,IAAI,CAACoB,OAAO,EAAE;IAChBd,kBAAM,CAACE,IAAI,CACT,mGAAmG,CACpG;EACH;EAEA,IAAIR,IAAI,CAACqB,KAAK,IAAIrB,IAAI,CAACsB,IAAI,EAAE;IAC3BhB,kBAAM,CAACE,IAAI,CACT,wGAAwG,CACzG;EACH;EAEA,IAAI;IAACa;EAAK,CAAC,GAAGrB,IAAI;EAElB,IAAIA,IAAI,CAACuB,WAAW,EAAE;IACpB,MAAMC,YAAY,GAAG,MAAM,IAAAC,wCAAsB,EAC/C,OAAO,EACPP,cAAc,CAACQ,SAAS,CACzB;IACD,IAAIF,YAAY,EAAE;MAChBH,KAAK,GAAG,CAACG,YAAY,CAAC;IACxB;EACF;EAEA,IAAIG,UAAU,GAAG,IAAAC,0BAAY,EAC3BV,cAAc,CAACW,OAAO,EACtB7B,IAAI,CAACsB,IAAI,IAAItB,IAAI,CAACoB,OAAO,EACzBC,KAAK,EACL,QAAQ,CACT;EAED,IAAIrB,IAAI,CAAC8B,WAAW,EAAE;IACpBH,UAAU,CAACI,IAAI,CAAC,GAAG/B,IAAI,CAAC8B,WAAW,CAAC;EACtC;EAEA,IAAI9B,IAAI,CAACgC,cAAc,EAAE;IACvB,MAAMC,OAAO,GAAG,IAAAC,mBAAU,GAAE;IAC5B,MAAMC,OAAO,GAAGC,YAAG,CAACC,UAAU,CAACJ,OAAO,CAAC;IACvC,MAAMK,aAAa,GAAGH,OAAO,CAC1BI,GAAG,CAAEC,MAAM,IAAK;MACf,OAAOJ,YAAG,CAACK,MAAM,CAACR,OAAO,EAAEO,MAAM,CAAC;IACpC,CAAC,CAAC,CACDE,MAAM,CACL,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,KAAKF,IAAI,IAAI,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,KAAKC,KAAK,CACtE;IACH,IAAIN,aAAa,CAACS,MAAM,GAAG,CAAC,EAAE;MAC5BzC,kBAAM,CAACC,IAAI,CAAE,0BAAyB+B,aAAa,CAACU,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;MACjE;MACA;MACArB,UAAU,CAACI,IAAI,CACb,kCAAkC,GAAGO,aAAa,CAACU,IAAI,CAAC,GAAG,CAAC,CAC7D;MACDrB,UAAU,CAACI,IAAI,CAAC,6BAA6B,GAAGO,aAAa,CAACU,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1E;EACF;EACA,MAAMjD,WAAW,CAACC,IAAI,EAAEC,MAAM,CAAC;EAC/B,OAAOgD,KAAK,CAACtB,UAAU,EAAET,cAAc,CAACQ,SAAS,CAAC;AACpD;AAEO,SAASuB,KAAK,CAACtB,UAAoB,EAAED,SAAiB,EAAE;EAC7DwB,OAAO,CAACC,KAAK,CAACzB,SAAS,CAAC;EACxB,MAAM0B,GAAG,GAAGF,OAAO,CAACG,QAAQ,CAACC,UAAU,CAAC,KAAK,CAAC,GAAG,aAAa,GAAG,WAAW;EAC5EhD,kBAAM,CAACC,IAAI,CAAC,qBAAqB,CAAC;EAClCD,kBAAM,CAACiD,KAAK,CAAE,oBAAmBH,GAAI,IAAGzB,UAAU,CAACqB,IAAI,CAAC,GAAG,CAAE,GAAE,CAAC;EAChE,IAAI;IACFQ,gBAAK,CAACC,IAAI,CAACL,GAAG,EAAEzB,UAAU,EAAE;MAC1B+B,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAEjC;IACP,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOb,KAAK,EAAE;IACd,IAAA+C,6BAAiB,GAAE;IACnB,MAAM,KAAIC,oBAAQ,EAAC,0BAA0B,EAAEhD,KAAK,CAAU;EAChE;AACF;AAEO,MAAMiD,OAAO,GAAG,CACrB;EACEC,IAAI,EAAE,iBAAiB;EACvBC,WAAW,EAAE;AACf,CAAC,EACD;EACED,IAAI,EAAE,oBAAoB;EAC1BC,WAAW,EACT;AACJ,CAAC,EACD;EACED,IAAI,EAAE,eAAe;EACrBC,WAAW,EAAE;AACf,CAAC,EACD;EACED,IAAI,EAAE,iBAAiB;EACvBE,OAAO,EAAEf,OAAO,CAACgB,GAAG,CAACC,cAAc,IAAI,IAAI;EAC3CC,KAAK,EAAEC;AACT,CAAC,EACD;EACEN,IAAI,EAAE,qBAAqB;EAC3BC,WAAW,EACT,+EAA+E;EACjFC,OAAO,EAAE,IAAAK,kCAAsB;AACjC,CAAC,EACD;EACEP,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EACT,6GAA6G;EAC/GI,KAAK,EAAGG,GAAW,IAAKA,GAAG,CAACC,KAAK,CAAC,GAAG;AACvC,CAAC,EACD;EACET,IAAI,EAAE,oBAAoB;EAC1BC,WAAW,EACT,mFAAmF;EACrFC,OAAO,EAAE;AACX,CAAC,EACD;EACEF,IAAI,EAAE,yBAAyB;EAC/BC,WAAW,EAAE,8CAA8C;EAC3DI,KAAK,EAAGG,GAAW,IAAKA,GAAG,CAACC,KAAK,CAAC,GAAG;AACvC,CAAC,EACD;EACET,IAAI,EAAE,eAAe;EACrBC,WAAW,EACT;AACJ,CAAC,CACF;AAAC;AAAA,eAEa;EACbD,IAAI,EAAE,eAAe;EACrBC,WAAW,EAAE,iBAAiB;EAC9BS,IAAI,EAAEzD,YAAY;EAClB8C;AACF,CAAC;AAAA"}