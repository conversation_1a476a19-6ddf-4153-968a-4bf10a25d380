{"version": 3, "names": ["packageManager", "fs", "existsSync", "PACKAGE_MANAGERS", "YARN", "NPM", "undefined", "yarn", "label", "description", "getDiagnostics", "Binaries", "needsToBeFixed", "doesSoftwareNeedToBeFixed", "version", "Yarn", "versionRange", "versionRanges", "visible", "runAutomaticFix", "loader", "install", "pkg", "url", "npm"], "sources": ["../../../src/tools/healthchecks/packageManagers.ts"], "sourcesContent": ["import fs from 'fs';\nimport versionRanges from '../versionRanges';\nimport {\n  PACKAGE_MANAGERS,\n  doesSoftwareNeedToBeFixed,\n} from '../checkInstallation';\nimport {install} from '../install';\nimport {HealthCheckInterface} from '../../types';\n\nconst packageManager = (() => {\n  if (fs.existsSync('yarn.lock')) {\n    return PACKAGE_MANAGERS.YARN;\n  }\n\n  if (fs.existsSync('package-lock.json')) {\n    return PACKAGE_MANAGERS.NPM;\n  }\n\n  return undefined;\n})();\n\nconst yarn: HealthCheckInterface = {\n  label: 'yarn',\n  description: 'Required to install NPM dependencies',\n  getDiagnostics: async ({Binaries}) => ({\n    needsToBeFixed: doesSoftwareNeedToBeFixed({\n      version: Binaries.Yarn.version,\n      versionRange: versionRanges.YARN,\n    }),\n    version: Binaries.Yarn.version,\n    versionRange: versionRanges.YARN,\n  }),\n  // Only show `yarn` if there's a `yarn.lock` file in the current directory\n  // or if we can't identify that the user uses yarn or npm\n  visible:\n    packageManager === PACKAGE_MANAGERS.YARN || packageManager === undefined,\n  runAutomaticFix: async ({loader}) =>\n    await install({\n      pkg: 'yarn',\n      label: 'yarn',\n      url: 'https://yarnpkg.com/docs/install',\n      loader,\n    }),\n};\n\nconst npm: HealthCheckInterface = {\n  label: 'npm',\n  description: 'Required to install NPM dependencies',\n  getDiagnostics: async ({Binaries}) => ({\n    needsToBeFixed: doesSoftwareNeedToBeFixed({\n      version: Binaries.npm.version,\n      versionRange: versionRanges.NPM,\n    }),\n    version: Binaries.npm.version,\n    versionRange: versionRanges.NPM,\n  }),\n  // Only show `yarn` if there's a `package-lock.json` file in the current directory\n  // or if we can't identify that the user uses yarn or npm\n  visible:\n    packageManager === PACKAGE_MANAGERS.NPM || packageManager === undefined,\n  runAutomaticFix: async ({loader}) =>\n    await install({\n      pkg: 'node',\n      label: 'node',\n      url: 'https://nodejs.org/',\n      loader,\n    }),\n};\n\nexport {packageManager, yarn, npm};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAIA;AAAmC;AAGnC,MAAMA,cAAc,GAAG,CAAC,MAAM;EAC5B,IAAIC,aAAE,CAACC,UAAU,CAAC,WAAW,CAAC,EAAE;IAC9B,OAAOC,mCAAgB,CAACC,IAAI;EAC9B;EAEA,IAAIH,aAAE,CAACC,UAAU,CAAC,mBAAmB,CAAC,EAAE;IACtC,OAAOC,mCAAgB,CAACE,GAAG;EAC7B;EAEA,OAAOC,SAAS;AAClB,CAAC,GAAG;AAAC;AAEL,MAAMC,IAA0B,GAAG;EACjCC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE,sCAAsC;EACnDC,cAAc,EAAE,OAAO;IAACC;EAAQ,CAAC,MAAM;IACrCC,cAAc,EAAE,IAAAC,4CAAyB,EAAC;MACxCC,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACD,OAAO;MAC9BE,YAAY,EAAEC,sBAAa,CAACb;IAC9B,CAAC,CAAC;IACFU,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACD,OAAO;IAC9BE,YAAY,EAAEC,sBAAa,CAACb;EAC9B,CAAC,CAAC;EACF;EACA;EACAc,OAAO,EACLlB,cAAc,KAAKG,mCAAgB,CAACC,IAAI,IAAIJ,cAAc,KAAKM,SAAS;EAC1Ea,eAAe,EAAE,OAAO;IAACC;EAAM,CAAC,KAC9B,MAAM,IAAAC,gBAAO,EAAC;IACZC,GAAG,EAAE,MAAM;IACXd,KAAK,EAAE,MAAM;IACbe,GAAG,EAAE,kCAAkC;IACvCH;EACF,CAAC;AACL,CAAC;AAAC;AAEF,MAAMI,GAAyB,GAAG;EAChChB,KAAK,EAAE,KAAK;EACZC,WAAW,EAAE,sCAAsC;EACnDC,cAAc,EAAE,OAAO;IAACC;EAAQ,CAAC,MAAM;IACrCC,cAAc,EAAE,IAAAC,4CAAyB,EAAC;MACxCC,OAAO,EAAEH,QAAQ,CAACa,GAAG,CAACV,OAAO;MAC7BE,YAAY,EAAEC,sBAAa,CAACZ;IAC9B,CAAC,CAAC;IACFS,OAAO,EAAEH,QAAQ,CAACa,GAAG,CAACV,OAAO;IAC7BE,YAAY,EAAEC,sBAAa,CAACZ;EAC9B,CAAC,CAAC;EACF;EACA;EACAa,OAAO,EACLlB,cAAc,KAAKG,mCAAgB,CAACE,GAAG,IAAIL,cAAc,KAAKM,SAAS;EACzEa,eAAe,EAAE,OAAO;IAACC;EAAM,CAAC,KAC9B,MAAM,IAAAC,gBAAO,EAAC;IACZC,GAAG,EAAE,MAAM;IACXd,KAAK,EAAE,MAAM;IACbe,GAAG,EAAE,qBAAqB;IAC1BH;EACF,CAAC;AACL,CAAC;AAAC"}