{"version": 3, "names": ["printWatchModeInstructions", "logger", "log", "chalk", "bold", "enableWatchMode", "messageSocket", "process", "stdin", "setRawMode", "debug", "readline", "emitKeypressEvents", "restore", "hookStdout", "output", "includes", "on", "_key", "data", "ctrl", "name", "exit", "emit", "broadcast", "info", "execa", "stdout", "pipe", "console"], "sources": ["../../../src/commands/start/watchMode.ts"], "sourcesContent": ["import readline from 'readline';\nimport {logger, hookStdout} from '@react-native-community/cli-tools';\nimport execa from 'execa';\nimport chalk from 'chalk';\n\nfunction printWatchModeInstructions() {\n  logger.log(\n    `${chalk.bold('r')} - reload the app\\n${chalk.bold(\n      'd',\n    )} - open developer menu\\n${chalk.bold('i')} - run on iOS\\n${chalk.bold(\n      'a',\n    )} - run on Android`,\n  );\n}\n\nfunction enableWatchMode(messageSocket: any) {\n  // We need to set this to true to catch key presses individually.\n  // As a result we have to implement our own method for exiting\n  // and other commands (e.g. ctrl+c & ctrl+z)\n  if (!process.stdin.setRawMode) {\n    logger.debug('Watch mode is not supported in this environment');\n    return;\n  }\n\n  readline.emitKeypressEvents(process.stdin);\n\n  process.stdin.setRawMode(true);\n\n  // We have no way of knowing when the dependency graph is done loading\n  // except by hooking into stdout itself. We want to print instructions\n  // right after its done loading.\n  const restore = hookStdout((output: string) => {\n    if (output.includes('Fast - Scalable - Integrated')) {\n      printWatchModeInstructions();\n      restore();\n    }\n  });\n\n  process.stdin.on('keypress', (_key, data) => {\n    const {ctrl, name} = data;\n    if (ctrl === true) {\n      switch (name) {\n        case 'c':\n          process.exit();\n          break;\n        case 'z':\n          process.emit('SIGTSTP', 'SIGTSTP');\n          break;\n      }\n    } else if (name === 'r') {\n      messageSocket.broadcast('reload', null);\n      logger.info('Reloading app...');\n    } else if (name === 'd') {\n      messageSocket.broadcast('devMenu', null);\n      logger.info('Opening developer menu...');\n    } else if (name === 'i' || name === 'a') {\n      logger.info(`Opening the app on ${name === 'i' ? 'iOS' : 'Android'}...`);\n      execa('npx', [\n        'react-native',\n        name === 'i' ? 'run-ios' : 'run-android',\n      ]).stdout?.pipe(process.stdout);\n    } else {\n      console.log(_key);\n    }\n  });\n}\n\nexport default enableWatchMode;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAE1B,SAASA,0BAA0B,GAAG;EACpCC,kBAAM,CAACC,GAAG,CACP,GAAEC,gBAAK,CAACC,IAAI,CAAC,GAAG,CAAE,sBAAqBD,gBAAK,CAACC,IAAI,CAChD,GAAG,CACH,2BAA0BD,gBAAK,CAACC,IAAI,CAAC,GAAG,CAAE,kBAAiBD,gBAAK,CAACC,IAAI,CACrE,GAAG,CACH,mBAAkB,CACrB;AACH;AAEA,SAASC,eAAe,CAACC,aAAkB,EAAE;EAC3C;EACA;EACA;EACA,IAAI,CAACC,OAAO,CAACC,KAAK,CAACC,UAAU,EAAE;IAC7BR,kBAAM,CAACS,KAAK,CAAC,iDAAiD,CAAC;IAC/D;EACF;EAEAC,mBAAQ,CAACC,kBAAkB,CAACL,OAAO,CAACC,KAAK,CAAC;EAE1CD,OAAO,CAACC,KAAK,CAACC,UAAU,CAAC,IAAI,CAAC;;EAE9B;EACA;EACA;EACA,MAAMI,OAAO,GAAG,IAAAC,sBAAU,EAAEC,MAAc,IAAK;IAC7C,IAAIA,MAAM,CAACC,QAAQ,CAAC,8BAA8B,CAAC,EAAE;MACnDhB,0BAA0B,EAAE;MAC5Ba,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EAEFN,OAAO,CAACC,KAAK,CAACS,EAAE,CAAC,UAAU,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAK;IAC3C,MAAM;MAACC,IAAI;MAAEC;IAAI,CAAC,GAAGF,IAAI;IACzB,IAAIC,IAAI,KAAK,IAAI,EAAE;MACjB,QAAQC,IAAI;QACV,KAAK,GAAG;UACNd,OAAO,CAACe,IAAI,EAAE;UACd;QACF,KAAK,GAAG;UACNf,OAAO,CAACgB,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC;UAClC;MAAM;IAEZ,CAAC,MAAM,IAAIF,IAAI,KAAK,GAAG,EAAE;MACvBf,aAAa,CAACkB,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC;MACvCvB,kBAAM,CAACwB,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC,MAAM,IAAIJ,IAAI,KAAK,GAAG,EAAE;MACvBf,aAAa,CAACkB,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC;MACxCvB,kBAAM,CAACwB,IAAI,CAAC,2BAA2B,CAAC;IAC1C,CAAC,MAAM,IAAIJ,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;MAAA;MACvCpB,kBAAM,CAACwB,IAAI,CAAE,sBAAqBJ,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,SAAU,KAAI,CAAC;MACxE,qBAAAK,gBAAK,EAAC,KAAK,EAAE,CACX,cAAc,EACdL,IAAI,KAAK,GAAG,GAAG,SAAS,GAAG,aAAa,CACzC,CAAC,CAACM,MAAM,kDAHT,cAGWC,IAAI,CAACrB,OAAO,CAACoB,MAAM,CAAC;IACjC,CAAC,MAAM;MACLE,OAAO,CAAC3B,GAAG,CAACgB,IAAI,CAAC;IACnB;EACF,CAAC,CAAC;AACJ;AAAC,eAEcb,eAAe;AAAA"}