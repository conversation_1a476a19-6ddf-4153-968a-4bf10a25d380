{"version": 3, "names": ["label", "description", "getDiagnostics", "Managers", "needsToBeFixed", "doesSoftwareNeedToBeFixed", "version", "CocoaPods", "versionRange", "versionRanges", "COCOAPODS", "runAutomaticFix", "loader", "stop", "installMethodCapitalized", "loaderInstallationMessage", "loaderSucceedMessage", "start", "options", "execa", "succeed", "_error", "runSudo", "join", "error", "logError", "healthcheck", "command"], "sources": ["../../../src/tools/healthchecks/cocoaPods.ts"], "sourcesContent": ["import execa from 'execa';\nimport {doesSoftwareNeedToBeFixed} from '../checkInstallation';\nimport {runSudo} from '../installPods';\nimport {logError} from './common';\nimport {HealthCheckInterface} from '../../types';\nimport versionRanges from '../versionRanges';\n\nconst label = 'CocoaPods';\n\nexport default {\n  label,\n  description: 'Required for installing iOS dependencies',\n  getDiagnostics: async ({Managers}) => ({\n    needsToBeFixed: doesSoftwareNeedToBeFixed({\n      version: Managers.CocoaPods.version,\n      versionRange: versionRanges.COCOAPODS,\n    }),\n    version: Managers.CocoaPods.version,\n    versionRange: versionRanges.COCOAPODS,\n  }),\n  runAutomaticFix: async ({loader}) => {\n    loader.stop();\n\n    const installMethodCapitalized = 'Gem';\n    const loaderInstallationMessage = `${label} (installing with ${installMethodCapitalized})`;\n    const loaderSucceedMessage = `${label} (installed with ${installMethodCapitalized})`;\n\n    loader.start(loaderInstallationMessage);\n\n    const options = ['install', 'cocoapods', '--no-document'];\n\n    try {\n      // First attempt to install `cocoapods`\n      await execa('gem', options);\n\n      return loader.succeed(loaderSucceedMessage);\n    } catch (_error) {\n      // If that doesn't work then try with sudo\n      try {\n        await runSudo(`gem ${options.join(' ')}`);\n\n        return loader.succeed(loaderSucceedMessage);\n      } catch (error) {\n        logError({\n          healthcheck: label,\n          loader,\n          error: error as any,\n          command: 'sudo gem install cocoapods',\n        });\n      }\n    }\n    return;\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AAEA;AAA6C;AAE7C,MAAMA,KAAK,GAAG,WAAW;AAAC,eAEX;EACbA,KAAK;EACLC,WAAW,EAAE,0CAA0C;EACvDC,cAAc,EAAE,OAAO;IAACC;EAAQ,CAAC,MAAM;IACrCC,cAAc,EAAE,IAAAC,4CAAyB,EAAC;MACxCC,OAAO,EAAEH,QAAQ,CAACI,SAAS,CAACD,OAAO;MACnCE,YAAY,EAAEC,sBAAa,CAACC;IAC9B,CAAC,CAAC;IACFJ,OAAO,EAAEH,QAAQ,CAACI,SAAS,CAACD,OAAO;IACnCE,YAAY,EAAEC,sBAAa,CAACC;EAC9B,CAAC,CAAC;EACFC,eAAe,EAAE,OAAO;IAACC;EAAM,CAAC,KAAK;IACnCA,MAAM,CAACC,IAAI,EAAE;IAEb,MAAMC,wBAAwB,GAAG,KAAK;IACtC,MAAMC,yBAAyB,GAAI,GAAEf,KAAM,qBAAoBc,wBAAyB,GAAE;IAC1F,MAAME,oBAAoB,GAAI,GAAEhB,KAAM,oBAAmBc,wBAAyB,GAAE;IAEpFF,MAAM,CAACK,KAAK,CAACF,yBAAyB,CAAC;IAEvC,MAAMG,OAAO,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,eAAe,CAAC;IAEzD,IAAI;MACF;MACA,MAAM,IAAAC,gBAAK,EAAC,KAAK,EAAED,OAAO,CAAC;MAE3B,OAAON,MAAM,CAACQ,OAAO,CAACJ,oBAAoB,CAAC;IAC7C,CAAC,CAAC,OAAOK,MAAM,EAAE;MACf;MACA,IAAI;QACF,MAAM,IAAAC,oBAAO,EAAE,OAAMJ,OAAO,CAACK,IAAI,CAAC,GAAG,CAAE,EAAC,CAAC;QAEzC,OAAOX,MAAM,CAACQ,OAAO,CAACJ,oBAAoB,CAAC;MAC7C,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACd,IAAAC,gBAAQ,EAAC;UACPC,WAAW,EAAE1B,KAAK;UAClBY,MAAM;UACNY,KAAK,EAAEA,KAAY;UACnBG,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF;IACA;EACF;AACF,CAAC;AAAA"}