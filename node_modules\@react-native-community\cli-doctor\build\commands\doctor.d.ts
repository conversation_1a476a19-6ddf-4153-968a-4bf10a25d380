import { DetachedCommandFunction } from '@react-native-community/cli-types';
type FlagsT = {
    fix: boolean | void;
    contributor: boolean | void;
};
declare const _default: {
    func: DetachedCommandFunction<FlagsT>;
    detached: boolean;
    name: string;
    description: string;
    options: {
        name: string;
        description: string;
    }[];
};
export default _default;
//# sourceMappingURL=doctor.d.ts.map