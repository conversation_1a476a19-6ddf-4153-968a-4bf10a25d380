{"version": 3, "names": ["info", "getInfo", "_argv", "ctx", "logger", "notFound", "platforms", "Android", "hermes<PERSON>nabled", "newArchEnabled", "iOS", "process", "platform", "project", "ios", "sourceDir", "podfile", "readFile", "path", "join", "includes", "e", "android", "gradleProperties", "output", "getEnvironmentInfo", "log", "stringify", "err", "error", "version", "logIfUpdateAvailable", "root", "name", "description", "func"], "sources": ["../../src/commands/info.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport getEnvironmentInfo from '../tools/envinfo';\nimport {logger, version} from '@react-native-community/cli-tools';\nimport {Config} from '@react-native-community/cli-types';\nimport {readFile} from 'fs-extra';\nimport path from 'path';\nimport {stringify} from 'yaml';\n\ntype PlatformValues = {\n  hermesEnabled: boolean | string;\n  newArchEnabled: boolean | string;\n};\n\ninterface Platforms {\n  Android: PlatformValues;\n  iOS: PlatformValues;\n}\n\nconst info = async function getInfo(_argv: Array<string>, ctx: Config) {\n  try {\n    logger.info('Fetching system and libraries information...');\n\n    const notFound = 'Not found';\n\n    const platforms: Platforms = {\n      Android: {\n        hermesEnabled: notFound,\n        newArchEnabled: notFound,\n      },\n      iOS: {\n        hermesEnabled: notFound,\n        newArchEnabled: notFound,\n      },\n    };\n\n    if (process.platform !== 'win32' && ctx.project.ios?.sourceDir) {\n      try {\n        const podfile = await readFile(\n          path.join(ctx.project.ios.sourceDir, '/Podfile.lock'),\n          'utf8',\n        );\n\n        platforms.iOS.hermesEnabled = podfile.includes('hermes-engine');\n      } catch (e) {\n        platforms.iOS.hermesEnabled = notFound;\n      }\n\n      try {\n        const project = await readFile(\n          path.join(\n            ctx.project.ios.sourceDir,\n            '/Pods/Pods.xcodeproj/project.pbxproj',\n          ),\n        );\n\n        platforms.iOS.newArchEnabled = project.includes(\n          '-DRCT_NEW_ARCH_ENABLED=1',\n        );\n      } catch {\n        platforms.iOS.newArchEnabled = notFound;\n      }\n    }\n\n    if (ctx.project.android?.sourceDir) {\n      try {\n        const gradleProperties = await readFile(\n          path.join(ctx.project.android.sourceDir, '/gradle.properties'),\n          'utf8',\n        );\n\n        platforms.Android.hermesEnabled = gradleProperties.includes(\n          'hermesEnabled=true',\n        );\n        platforms.Android.newArchEnabled = gradleProperties.includes(\n          'newArchEnabled=true',\n        );\n      } catch {\n        platforms.Android.hermesEnabled = notFound;\n        platforms.Android.newArchEnabled = notFound;\n      }\n    }\n\n    const output = await getEnvironmentInfo();\n\n    logger.log(stringify({...output, ...platforms}));\n  } catch (err) {\n    logger.error(`Unable to print environment info.\\n${err}`);\n  } finally {\n    await version.logIfUpdateAvailable(ctx.root);\n  }\n};\n\nexport default {\n  name: 'info',\n  description: 'Get relevant version info about OS, toolchain and libraries',\n  func: info,\n};\n"], "mappings": ";;;;;;AAOA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA+B;AAZ/B;AACA;AACA;AACA;AACA;AACA;;AAmBA,MAAMA,IAAI,GAAG,eAAeC,OAAO,CAACC,KAAoB,EAAEC,GAAW,EAAE;EACrE,IAAI;IAAA;IACFC,kBAAM,CAACJ,IAAI,CAAC,8CAA8C,CAAC;IAE3D,MAAMK,QAAQ,GAAG,WAAW;IAE5B,MAAMC,SAAoB,GAAG;MAC3BC,OAAO,EAAE;QACPC,aAAa,EAAEH,QAAQ;QACvBI,cAAc,EAAEJ;MAClB,CAAC;MACDK,GAAG,EAAE;QACHF,aAAa,EAAEH,QAAQ;QACvBI,cAAc,EAAEJ;MAClB;IACF,CAAC;IAED,IAAIM,OAAO,CAACC,QAAQ,KAAK,OAAO,yBAAIT,GAAG,CAACU,OAAO,CAACC,GAAG,qDAAf,iBAAiBC,SAAS,GAAE;MAC9D,IAAI;QACF,MAAMC,OAAO,GAAG,MAAM,IAAAC,mBAAQ,EAC5BC,eAAI,CAACC,IAAI,CAAChB,GAAG,CAACU,OAAO,CAACC,GAAG,CAACC,SAAS,EAAE,eAAe,CAAC,EACrD,MAAM,CACP;QAEDT,SAAS,CAACI,GAAG,CAACF,aAAa,GAAGQ,OAAO,CAACI,QAAQ,CAAC,eAAe,CAAC;MACjE,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVf,SAAS,CAACI,GAAG,CAACF,aAAa,GAAGH,QAAQ;MACxC;MAEA,IAAI;QACF,MAAMQ,OAAO,GAAG,MAAM,IAAAI,mBAAQ,EAC5BC,eAAI,CAACC,IAAI,CACPhB,GAAG,CAACU,OAAO,CAACC,GAAG,CAACC,SAAS,EACzB,sCAAsC,CACvC,CACF;QAEDT,SAAS,CAACI,GAAG,CAACD,cAAc,GAAGI,OAAO,CAACO,QAAQ,CAC7C,0BAA0B,CAC3B;MACH,CAAC,CAAC,MAAM;QACNd,SAAS,CAACI,GAAG,CAACD,cAAc,GAAGJ,QAAQ;MACzC;IACF;IAEA,4BAAIF,GAAG,CAACU,OAAO,CAACS,OAAO,yDAAnB,qBAAqBP,SAAS,EAAE;MAClC,IAAI;QACF,MAAMQ,gBAAgB,GAAG,MAAM,IAAAN,mBAAQ,EACrCC,eAAI,CAACC,IAAI,CAAChB,GAAG,CAACU,OAAO,CAACS,OAAO,CAACP,SAAS,EAAE,oBAAoB,CAAC,EAC9D,MAAM,CACP;QAEDT,SAAS,CAACC,OAAO,CAACC,aAAa,GAAGe,gBAAgB,CAACH,QAAQ,CACzD,oBAAoB,CACrB;QACDd,SAAS,CAACC,OAAO,CAACE,cAAc,GAAGc,gBAAgB,CAACH,QAAQ,CAC1D,qBAAqB,CACtB;MACH,CAAC,CAAC,MAAM;QACNd,SAAS,CAACC,OAAO,CAACC,aAAa,GAAGH,QAAQ;QAC1CC,SAAS,CAACC,OAAO,CAACE,cAAc,GAAGJ,QAAQ;MAC7C;IACF;IAEA,MAAMmB,MAAM,GAAG,MAAM,IAAAC,gBAAkB,GAAE;IAEzCrB,kBAAM,CAACsB,GAAG,CAAC,IAAAC,iBAAS,EAAC;MAAC,GAAGH,MAAM;MAAE,GAAGlB;IAAS,CAAC,CAAC,CAAC;EAClD,CAAC,CAAC,OAAOsB,GAAG,EAAE;IACZxB,kBAAM,CAACyB,KAAK,CAAE,sCAAqCD,GAAI,EAAC,CAAC;EAC3D,CAAC,SAAS;IACR,MAAME,mBAAO,CAACC,oBAAoB,CAAC5B,GAAG,CAAC6B,IAAI,CAAC;EAC9C;AACF,CAAC;AAAC,eAEa;EACbC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,6DAA6D;EAC1EC,IAAI,EAAEnC;AACR,CAAC;AAAA"}