{"version": 3, "names": ["HEALTHCHECK_TYPES", "ERROR", "WARNING", "getHealthchecks", "contributor", "additionalChecks", "config", "loadConfig", "healthChecks", "common", "label", "healthchecks", "nodeJS", "yarn", "npm", "process", "platform", "watchman", "android", "adb", "jdk", "androidStudio", "androidSDK", "androidHomeEnvVariable", "androidNDK", "ios", "xcode", "ruby", "cocoaPods", "iosDeploy", "xcodeEnv"], "sources": ["../../../src/tools/healthchecks/index.ts"], "sourcesContent": ["import nodeJS from './nodeJS';\nimport {yarn, npm} from './packageManagers';\nimport adb from './adb';\nimport jdk from './jdk';\nimport watchman from './watchman';\nimport ruby from './ruby';\nimport androidHomeEnvVariable from './androidHomeEnvVariable';\nimport androidStudio from './androidStudio';\nimport androidSDK from './androidSDK';\nimport androidNDK from './androidNDK';\nimport xcode from './xcode';\nimport cocoaPods from './cocoaPods';\nimport iosDeploy from './iosDeploy';\nimport {Healthchecks, HealthCheckCategory} from '../../types';\nimport loadConfig from '@react-native-community/cli-config';\nimport xcodeEnv from './xcodeEnv';\n\nexport const HEALTHCHECK_TYPES = {\n  ERROR: 'ERROR',\n  WARNING: 'WARNING',\n};\n\ntype Options = {\n  fix: boolean | void;\n  contributor: boolean | void;\n};\n\nexport const getHealthchecks = ({contributor}: Options): Healthchecks => {\n  let additionalChecks: HealthCheckCategory[] = [];\n\n  // Doctor can run in a detached mode, where there isn't a config so this can fail\n  try {\n    let config = loadConfig();\n    additionalChecks = config.healthChecks;\n  } catch {}\n\n  return {\n    common: {\n      label: 'Common',\n      healthchecks: [\n        nodeJS,\n        yarn,\n        npm,\n        ...(process.platform === 'darwin' ? [watchman] : []),\n      ],\n    },\n    android: {\n      label: 'Android',\n      healthchecks: [\n        adb,\n        jdk,\n        androidStudio,\n        androidSDK,\n        androidHomeEnvVariable,\n        ...(contributor ? [androidNDK] : []),\n      ],\n    },\n    ...(process.platform === 'darwin'\n      ? {\n          ios: {\n            label: 'iOS',\n            healthchecks: [xcode, ruby, cocoaPods, iosDeploy, xcodeEnv],\n          },\n        }\n      : {}),\n    ...additionalChecks,\n  };\n};\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAkC;AAE3B,MAAMA,iBAAiB,GAAG;EAC/BC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE;AACX,CAAC;AAAC;AAOK,MAAMC,eAAe,GAAG,CAAC;EAACC;AAAoB,CAAC,KAAmB;EACvE,IAAIC,gBAAuC,GAAG,EAAE;;EAEhD;EACA,IAAI;IACF,IAAIC,MAAM,GAAG,IAAAC,oBAAU,GAAE;IACzBF,gBAAgB,GAAGC,MAAM,CAACE,YAAY;EACxC,CAAC,CAAC,MAAM,CAAC;EAET,OAAO;IACLC,MAAM,EAAE;MACNC,KAAK,EAAE,QAAQ;MACfC,YAAY,EAAE,CACZC,eAAM,EACNC,qBAAI,EACJC,oBAAG,EACH,IAAIC,OAAO,CAACC,QAAQ,KAAK,QAAQ,GAAG,CAACC,iBAAQ,CAAC,GAAG,EAAE,CAAC;IAExD,CAAC;IACDC,OAAO,EAAE;MACPR,KAAK,EAAE,SAAS;MAChBC,YAAY,EAAE,CACZQ,YAAG,EACHC,YAAG,EACHC,sBAAa,EACbC,mBAAU,EACVC,+BAAsB,EACtB,IAAInB,WAAW,GAAG,CAACoB,mBAAU,CAAC,GAAG,EAAE,CAAC;IAExC,CAAC;IACD,IAAIT,OAAO,CAACC,QAAQ,KAAK,QAAQ,GAC7B;MACES,GAAG,EAAE;QACHf,KAAK,EAAE,KAAK;QACZC,YAAY,EAAE,CAACe,cAAK,EAAEC,aAAI,EAAEC,kBAAS,EAAEC,kBAAS,EAAEC,iBAAQ;MAC5D;IACF,CAAC,GACD,CAAC,CAAC,CAAC;IACP,GAAGzB;EACL,CAAC;AACH,CAAC;AAAC"}