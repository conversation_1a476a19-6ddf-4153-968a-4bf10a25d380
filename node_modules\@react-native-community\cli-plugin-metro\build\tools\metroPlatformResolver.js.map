{"version": 3, "names": ["reactNativePlatformResolver", "platformImplementations", "context", "moduleName", "platform", "modifiedModuleName", "startsWith", "slice", "length", "resolveRequest"], "sources": ["../../src/tools/metroPlatformResolver.ts"], "sourcesContent": ["/**\n * This is an implementation of a metro resolveRequest option which will remap react-native imports\n * to different npm packages based on the platform requested.  This allows a single metro instance/config\n * to produce bundles for multiple out of tree platforms at a time.\n *\n * @param platformImplementations\n * A map of platform to npm package that implements that platform\n *\n * Ex:\n * {\n *    windows: 'react-native-windows'\n *    macos: 'react-native-macos'\n * }\n */\n\nimport type {CustomResolver} from 'metro-resolver';\n\nexport function reactNativePlatformResolver(platformImplementations: {\n  [platform: string]: string;\n}): CustomResolver {\n  return (context, moduleName, platform) => {\n    let modifiedModuleName = moduleName;\n    if (platform != null && platformImplementations[platform]) {\n      if (moduleName === 'react-native') {\n        modifiedModuleName = platformImplementations[platform];\n      } else if (moduleName.startsWith('react-native/')) {\n        modifiedModuleName = `${\n          platformImplementations[platform]\n        }/${modifiedModuleName.slice('react-native/'.length)}`;\n      }\n    }\n    return context.resolveRequest(context, modifiedModuleName, platform);\n  };\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIO,SAASA,2BAA2B,CAACC,uBAE3C,EAAkB;EACjB,OAAO,CAACC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,KAAK;IACxC,IAAIC,kBAAkB,GAAGF,UAAU;IACnC,IAAIC,QAAQ,IAAI,IAAI,IAAIH,uBAAuB,CAACG,QAAQ,CAAC,EAAE;MACzD,IAAID,UAAU,KAAK,cAAc,EAAE;QACjCE,kBAAkB,GAAGJ,uBAAuB,CAACG,QAAQ,CAAC;MACxD,CAAC,MAAM,IAAID,UAAU,CAACG,UAAU,CAAC,eAAe,CAAC,EAAE;QACjDD,kBAAkB,GAAI,GACpBJ,uBAAuB,CAACG,QAAQ,CACjC,IAAGC,kBAAkB,CAACE,KAAK,CAAC,eAAe,CAACC,MAAM,CAAE,EAAC;MACxD;IACF;IACA,OAAON,OAAO,CAACO,cAAc,CAACP,OAAO,EAAEG,kBAAkB,EAAED,QAAQ,CAAC;EACtE,CAAC;AACH"}