{"version": 3, "names": ["label", "description", "getDiagnostics", "IDEs", "needsToBeFixed", "missing", "version", "process", "platform", "androidStudioPath", "join", "getUserAndroidPath", "replace", "stdout", "executeCommand", "trim", "win32AutomaticFix", "loader", "androidStudioUrl", "installPath", "downloadAndUnzip", "downloadUrl", "component", "prefix", "arch", "binFolder", "createShortcut", "path", "name", "ico", "succeed", "runAutomaticFix", "logManualInstallation", "fail", "healthcheck", "url", "link", "docs", "hash", "guide"], "sources": ["../../../src/tools/healthchecks/androidStudio.ts"], "sourcesContent": ["import {join} from 'path';\n\nimport {link} from '@react-native-community/cli-tools';\n\nimport {HealthCheckInterface} from '../../types';\n\nimport {downloadAndUnzip} from '../downloadAndUnzip';\nimport {executeCommand} from '../windows/executeWinCommand';\nimport {getUserAndroidPath} from '../windows/androidWinHelpers';\nimport {createShortcut} from '../windows/create-shortcut';\n\nexport default {\n  label: 'Android Studio',\n  description: 'Required for building and installing your app on Android',\n  getDiagnostics: async ({IDEs}) => {\n    const needsToBeFixed = IDEs['Android Studio'] === 'Not Found';\n\n    const missing = {\n      needsToBeFixed,\n      version: IDEs['Android Studio'],\n    };\n\n    // On Windows `doctor` installs Android Studio locally in a well-known place\n    if (needsToBeFixed && process.platform === 'win32') {\n      const androidStudioPath = join(\n        getUserAndroidPath(),\n        'android-studio',\n        'bin',\n        'studio.exe',\n      ).replace(/\\\\/g, '\\\\\\\\');\n      const {stdout} = await executeCommand(\n        `wmic datafile where name=\"${androidStudioPath}\" get Version`,\n      );\n      const version = stdout.replace(/(\\r\\n|\\n|\\r)/gm, '').trim();\n\n      if (version === '') {\n        return missing;\n      }\n\n      return {\n        needsToBeFixed: false,\n        version,\n      };\n    }\n\n    return missing;\n  },\n  win32AutomaticFix: async ({loader}) => {\n    // Need a GitHub action to update automatically. See #1180\n    const androidStudioUrl =\n      'https://redirector.gvt1.com/edgedl/android/studio/ide-zips/*******/android-studio-ide-192.6392135-windows.zip';\n\n    const installPath = getUserAndroidPath();\n    await downloadAndUnzip({\n      loader,\n      downloadUrl: androidStudioUrl,\n      component: 'Android Studio',\n      installPath: installPath,\n    });\n\n    const prefix = process.arch === 'x64' ? '64' : '';\n    const binFolder = join(installPath, 'android-studio', 'bin');\n\n    await createShortcut({\n      path: join(binFolder, `studio${prefix}.exe`),\n      name: 'Android Studio',\n      ico: join(binFolder, 'studio.ico'),\n    });\n\n    loader.succeed(\n      `Android Studio installed successfully in \"${installPath}\".`,\n    );\n  },\n  runAutomaticFix: async ({loader, logManualInstallation}) => {\n    loader.fail();\n\n    return logManualInstallation({\n      healthcheck: 'Android Studio',\n      url: link.docs('environment-setup', {\n        hash: 'android-studio',\n        guide: 'native',\n        platform: 'android',\n      }),\n    });\n  },\n} as HealthCheckInterface;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAIA;AACA;AACA;AACA;AAA0D,eAE3C;EACbA,KAAK,EAAE,gBAAgB;EACvBC,WAAW,EAAE,0DAA0D;EACvEC,cAAc,EAAE,OAAO;IAACC;EAAI,CAAC,KAAK;IAChC,MAAMC,cAAc,GAAGD,IAAI,CAAC,gBAAgB,CAAC,KAAK,WAAW;IAE7D,MAAME,OAAO,GAAG;MACdD,cAAc;MACdE,OAAO,EAAEH,IAAI,CAAC,gBAAgB;IAChC,CAAC;;IAED;IACA,IAAIC,cAAc,IAAIG,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;MAClD,MAAMC,iBAAiB,GAAG,IAAAC,YAAI,EAC5B,IAAAC,qCAAkB,GAAE,EACpB,gBAAgB,EAChB,KAAK,EACL,YAAY,CACb,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;MACxB,MAAM;QAACC;MAAM,CAAC,GAAG,MAAM,IAAAC,iCAAc,EAClC,6BAA4BL,iBAAkB,eAAc,CAC9D;MACD,MAAMH,OAAO,GAAGO,MAAM,CAACD,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAACG,IAAI,EAAE;MAE3D,IAAIT,OAAO,KAAK,EAAE,EAAE;QAClB,OAAOD,OAAO;MAChB;MAEA,OAAO;QACLD,cAAc,EAAE,KAAK;QACrBE;MACF,CAAC;IACH;IAEA,OAAOD,OAAO;EAChB,CAAC;EACDW,iBAAiB,EAAE,OAAO;IAACC;EAAM,CAAC,KAAK;IACrC;IACA,MAAMC,gBAAgB,GACpB,+GAA+G;IAEjH,MAAMC,WAAW,GAAG,IAAAR,qCAAkB,GAAE;IACxC,MAAM,IAAAS,kCAAgB,EAAC;MACrBH,MAAM;MACNI,WAAW,EAAEH,gBAAgB;MAC7BI,SAAS,EAAE,gBAAgB;MAC3BH,WAAW,EAAEA;IACf,CAAC,CAAC;IAEF,MAAMI,MAAM,GAAGhB,OAAO,CAACiB,IAAI,KAAK,KAAK,GAAG,IAAI,GAAG,EAAE;IACjD,MAAMC,SAAS,GAAG,IAAAf,YAAI,EAACS,WAAW,EAAE,gBAAgB,EAAE,KAAK,CAAC;IAE5D,MAAM,IAAAO,8BAAc,EAAC;MACnBC,IAAI,EAAE,IAAAjB,YAAI,EAACe,SAAS,EAAG,SAAQF,MAAO,MAAK,CAAC;MAC5CK,IAAI,EAAE,gBAAgB;MACtBC,GAAG,EAAE,IAAAnB,YAAI,EAACe,SAAS,EAAE,YAAY;IACnC,CAAC,CAAC;IAEFR,MAAM,CAACa,OAAO,CACX,6CAA4CX,WAAY,IAAG,CAC7D;EACH,CAAC;EACDY,eAAe,EAAE,OAAO;IAACd,MAAM;IAAEe;EAAqB,CAAC,KAAK;IAC1Df,MAAM,CAACgB,IAAI,EAAE;IAEb,OAAOD,qBAAqB,CAAC;MAC3BE,WAAW,EAAE,gBAAgB;MAC7BC,GAAG,EAAEC,gBAAI,CAACC,IAAI,CAAC,mBAAmB,EAAE;QAClCC,IAAI,EAAE,gBAAgB;QACtBC,KAAK,EAAE,QAAQ;QACf/B,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,CAAC;EACJ;AACF,CAAC;AAAA"}