{"version": 3, "names": ["resolveNodeModuleDir", "root", "packageName", "packageDependencyDirectory", "findPackageDependencyDir", "startDir", "undefined", "CLIError"], "sources": ["../src/resolveNodeModuleDir.ts"], "sourcesContent": ["import {findPackageDependencyDir} from './findPackageDependencyDir';\nimport {CLIError} from './errors';\n\n/**\n * Finds a path inside `node_modules`\n */\nexport default function resolveNodeModuleDir(\n  root: string,\n  packageName: string,\n): string {\n  const packageDependencyDirectory = findPackageDependencyDir(packageName, {\n    startDir: root,\n  });\n  if (packageDependencyDirectory === undefined) {\n    throw new CLIError(\n      `Node module directory for package ${packageName} was not found`,\n    );\n  } else {\n    return packageDependencyDirectory;\n  }\n}\n"], "mappings": ";;;;;;AAAA;AACA;AAEA;AACA;AACA;AACe,SAASA,oBAAoB,CAC1CC,IAAY,EACZC,WAAmB,EACX;EACR,MAAMC,0BAA0B,GAAG,IAAAC,kDAAwB,EAACF,WAAW,EAAE;IACvEG,QAAQ,EAAEJ;EACZ,CAAC,CAAC;EACF,IAAIE,0BAA0B,KAAKG,SAAS,EAAE;IAC5C,MAAM,IAAIC,gBAAQ,CACf,qCAAoCL,WAAY,gBAAe,CACjE;EACH,CAAC,MAAM;IACL,OAAOC,0BAA0B;EACnC;AACF"}