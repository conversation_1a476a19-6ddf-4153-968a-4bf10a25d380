{"name": "shebang-command", "version": "2.0.0", "description": "Get the command from a shebang", "license": "MIT", "repository": "kevva/shebang-command", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["cmd", "command", "parse", "shebang"], "dependencies": {"shebang-regex": "^3.0.0"}, "devDependencies": {"ava": "^2.3.0", "xo": "^0.24.0"}}