{"name": "onetime", "version": "5.1.2", "description": "Ensure a function is only called once", "license": "MIT", "repository": "sindresorhus/onetime", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"mimic-fn": "^2.1.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}}